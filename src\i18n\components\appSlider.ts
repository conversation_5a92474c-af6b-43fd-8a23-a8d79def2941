// AppSlider 组件的国际化内容（包含功能模块）
export const appSlider = {
  zh: {
    // 功能模块
    functions: {
      chat: {
        title: "辅助鉴别诊断",
        subTitle: "我是Vet Mind AI助手，今天我能帮你做什么?",
        description: "通过症状描述和检查结果，为您提供智能诊断和专业建议",
      },
      knowledge: {
        title: "AI知识助手",
        subTitle: "我是知识查询助手，需要我帮你查什么呢?",
        description: "关于疾病、药品相关的知识都可以问我哦~",
      },
      voice: {
        title: "语音病历录入",
        subTitle: "开始问诊",
        description:
          "点击下方按钮开始记录您的问诊过程，系统将自动转录为文字并创建病历",
      },
      image: {
        title: "影像诊断助手",
        subTitle: "影像诊断助手",
        description:
          "请上传您的检查资料，我将为您提供详细的解读参考！今天需要帮您分析医学影像报告吗？",
      },
    },
    // 侧边栏UI
    newChat: "新对话",
    functionModules: "功能模块",
    historyChats: "历史对话",
    expandSidebar: "展开侧边栏",
    collapseSidebar: "折叠侧边栏",
    homeTooltip: "好兽医首页",
    confirmDelete: '确定要删除对话"{title}"吗？删除后无法恢复。',
    deleteTitle: "确认删除",
    confirmDeleteButton: "删除",
    cancelButton: "取消",
    logoutConfirm: "确定要退出登录吗？",
    logoutTitle: "退出确认",
    confirmButton: "确定",
    logoutSuccess: "退出登录成功",
    loadMore: "下拉加载更多",
    noMoreData: "暂无更多数据",
    feedback: "意见反馈",
    logout: "退出登录",
    login: "登录",
    rename: "重命名",
    delete: "删除",
  },
  en: {
    // 功能模块
    functions: {
      chat: {
        title: "Differential Diagnosis Assistant",
        subTitle: "I'm Vet Mind AI Assistant. How can I help you today?",
        description:
          "Provide intelligent diagnosis and professional advice through symptom description and examination results",
      },
      knowledge: {
        title: "AI Knowledge Assistant",
        subTitle:
          "I am a knowledge query assistant, what do you need me to help you find?",
        description:
          "Feel free to ask me about diseases, medications and related knowledge~",
      },
      voice: {
        title: "Voice Medical Record Entry",
        subTitle: "Start Consultation",
        description:
          "Click the button below to start recording your consultation process, the system will automatically transcribe it into text and create medical records",
      },
      image: {
        title: "Medical Imaging Assistant",
        subTitle: "Medical Imaging Assistant",
        description:
          "Please upload your examination materials, I will provide you with detailed interpretation references! Do you need help analyzing medical imaging reports today?",
      },
    },
    // 侧边栏UI
    newChat: "New Chat",
    functionModules: "Function Modules",
    historyChats: "Chat History",
    expandSidebar: "Expand Sidebar",
    collapseSidebar: "Collapse Sidebar",
    homeTooltip: "Good Veterinarian Homepage",
    confirmDelete:
      'Are you sure you want to delete the conversation "{title}"? This action cannot be undone.',
    deleteTitle: "Confirm Delete",
    confirmDeleteButton: "Delete",
    cancelButton: "Cancel",
    logoutConfirm: "Are you sure you want to log out?",
    logoutTitle: "Logout Confirmation",
    confirmButton: "Confirm",
    logoutSuccess: "Logout successful",
    loadMore: "Pull down to load more",
    noMoreData: "No more data",
    feedback: "Feedback",
    logout: "Logout",
    login: "Login",
    rename: "Rename",
    delete: "Delete",
  },
  // 日语
  ja: {
    // 功能模块
    functions: {
      chat: {
        title: "鑑別診断アシスタント",
        subTitle:
          "Vet Mind AIアシスタントです。本日はどのようなご用件でしょうか？",
        description:
          "症状の記述と検査結果を通じて、知能診断と専門的なアドバイスを提供します",
      },
      knowledge: {
        title: "AI知識アシスタント",
        subTitle: "私は知識検索アシスタントです。何を調べる必要がありますか？",
        description: "疾病、薬品関連の知識について何でもお聞きください〜",
      },
      voice: {
        title: "音声カルテ入力",
        subTitle: "診察開始",
        description:
          "下のボタンをクリックして診察過程の記録を開始してください。システムが自動的にテキストに転写してカルテを作成します",
      },
      image: {
        title: "画像診断アシスタント",
        subTitle: "画像診断アシスタント",
        description:
          "検査資料をアップロードしてください。詳細な解読参考を提供いたします！今日は医学画像レポートの分析をお手伝いしますか？",
      },
    },
    // 侧边栏UI
    newChat: "新しいチャット",
    functionModules: "機能モジュール",
    historyChats: "チャット履歴",
    expandSidebar: "サイドバーを展開",
    collapseSidebar: "サイドバーを折りたたむ",
    homeTooltip: "良い獣医師ホームページ",
    confirmDelete:
      '会話"{title}"を削除してもよろしいですか？削除後は復元できません。',
    deleteTitle: "削除確認",
    confirmDeleteButton: "削除",
    cancelButton: "キャンセル",
    logoutConfirm: "ログアウトしてもよろしいですか?",
    logoutTitle: "ログアウト確認",
    confirmButton: "確認",
    logoutSuccess: "ログアウト成功",
    loadMore: "プルダウンでさらに読み込む",
    noMoreData: "これ以上データはありません",
    feedback: "フィードバック",
    logout: "ログアウト",
    login: "ログイン",
    rename: "リネーム",
    delete: "削除",
  },
  // 韩语
  ko: {
    // 功能模块
    functions: {
      chat: {
        title: "감별진단 도우미",
        subTitle: "저는 Vet Mind AI Assistant입니다. 오늘 무엇을 도와드릴까요?",
        description:
          "증상 설명과 검사 결과를 통해 지능적인 진단과 전문적인 조언을 제공합니다",
      },
      knowledge: {
        title: "AI 지식 도우미",
        subTitle: "저는 지식 검색 어시스턴트입니다. 무엇을 찾아드릴까요?",
        description: "질병, 약품 관련 지식에 대해 무엇이든 물어보세요~",
      },
      voice: {
        title: "음성 의료기록 입력",
        subTitle: "진료 시작",
        description:
          "아래 버튼을 클릭하여 진료 과정 기록을 시작하세요. 시스템이 자동으로 텍스트로 전사하여 의료기록을 생성합니다",
      },
      image: {
        title: "의료영상 진단 도우미",
        subTitle: "의료영상 진단 도우미",
        description:
          "검사 자료를 업로드해 주세요. 상세한 해석 참고자료를 제공해 드리겠습니다! 오늘 의료영상 보고서 분석을 도와드릴까요?",
      },
    },
    // 侧边栏UI
    newChat: "새 대화",
    functionModules: "기능 모듈",
    historyChats: "대화 기록",
    expandSidebar: "사이드바 확장",
    collapseSidebar: "사이드바 접기",
    homeTooltip: "좋은 수의사 홈페이지",
    confirmDelete:
      '대화 "{title}"을(를) 삭제하시겠습니까? 삭제 후 복구할 수 없습니다.',
    deleteTitle: "삭제 확인",
    confirmDeleteButton: "삭제",
    cancelButton: "취소",
    logoutConfirm: "로그아웃하시겠습니까?",
    logoutTitle: "로그아웃 확인",
    confirmButton: "확인",
    logoutSuccess: "로그아웃 성공",
    loadMore: "아래로 당겨서 더 보기",
    noMoreData: "더 이상 데이터가 없습니다",
    feedback: "피드백",
    logout: "로그아웃",
    login: "로그인",
    rename: "이름 변경",
    delete: "삭제",
  },
  // 泰语
  th: {
    // 功能模块
    functions: {
      chat: {
        title: "ผู้ช่วยวินิจฉัยแยกโรค",
        subTitle: "ฉันคือผู้ช่วย AI ของ Vet Mind ค่ะ มีอะไรให้ช่วยไหมคะวันนี้",
        description:
          "ให้การวินิจฉัยอัจฉริยะและคำแนะนำจากผู้เชี่ยวชาญผ่านการอธิบายอาการและผลการตรวจ",
      },
      knowledge: {
        title: "ผู้ช่วยความรู้ AI",
        subTitle: "ฉันเป็นผู้ช่วยค้นหาความรู้ คุณต้องการให้ฉันช่วยหาอะไร?",
        description: "เกี่ยวกับโรค ยา ความรู้ที่เกี่ยวข้อง สามารถถามฉันได้นะ~",
      },
      voice: {
        title: "การบันทึกประวัติผู้ป่วยด้วยเสียง",
        subTitle: "เริ่มการซักประวัติ",
        description:
          "คลิกปุ่มด้านล่างเพื่อเริ่มบันทึกกระบวนการซักประวัติของคุณ ระบบจะแปลงเป็นข้อความโดยอัตโนมัติและสร้างประวัติผู้ป่วย",
      },
      image: {
        title: "ผู้ช่วยวินิจฉัยภาพถ่าย",
        subTitle: "ผู้ช่วยวินิจฉัยภาพถ่าย",
        description:
          "กรุณาอัปโหลดเอกสารการตรวจของคุณ ฉันจะให้การตีความรายละเอียดสำหรับอ้างอิง! วันนี้ต้องการให้ช่วยวิเคราะห์รายงานภาพทางการแพทย์หรือไม่?",
      },
    },
    // 侧边栏UI
    newChat: "แชทใหม่",
    functionModules: "โมดูลฟังก์ชัน",
    historyChats: "ประวัติการแชท",
    expandSidebar: "ขยายแถบด้านข้าง",
    collapseSidebar: "ยุบแถบด้านข้าง",
    homeTooltip: "หน้าแรกสัตวแพทย์ที่ดี",
    confirmDelete:
      'แน่ใจว่าต้องการลบการสนทนา "{title}" หรือไม่? ลบแล้วจะไม่สามารถกู้คืนได้',
    deleteTitle: "ยืนยันการลบ",
    confirmDeleteButton: "ลบ",
    cancelButton: "ยกเลิก",
    logoutConfirm: "แน่ใจว่าต้องการออกจากระบบหรือไม่?",
    logoutTitle: "ยืนยันการออกจากระบบ",
    confirmButton: "ยืนยัน",
    logoutSuccess: "ออกจากระบบสำเร็จ",
    loadMore: "ดึงลงเพื่อโหลดเพิ่มเติม",
    noMoreData: "ไม่มีข้อมูลเพิ่มเติม",
    feedback: "ข้อเสนอแนะ",
    logout: "ออกจากระบบ",
    login: "เข้าสู่ระบบ",
    rename: "เปลี่ยนชื่อ",
    delete: "ลบ",
  },
  // 马来语
  ms: {
    // 功能模块
    functions: {
      chat: {
        title: "Pembantu Diagnosis Pembezaan",
        subTitle:
          "Saya Penolong AI Minda Vet. Bagaimana saya boleh membantu anda hari ini?",
        description:
          "Menyediakan diagnosis pintar dan nasihat profesional melalui penerangan gejala dan keputusan pemeriksaan",
      },
      knowledge: {
        title: "Pembantu Pengetahuan AI",
        subTitle:
          "Saya adalah pembantu pertanyaan pengetahuan, apa yang anda perlukan saya bantu cari?",
        description:
          "Boleh tanya saya tentang penyakit, ubat-ubatan dan pengetahuan berkaitan~",
      },
      voice: {
        title: "Input Rekod Perubatan Suara",
        subTitle: "Mula Perundingan",
        description:
          "Klik butang di bawah untuk mula merakam proses perundingan anda, sistem akan secara automatik menyalin ke teks dan mencipta rekod perubatan",
      },
      image: {
        title: "Pembantu Diagnosis Imej",
        subTitle: "Pembantu Diagnosis Imej",
        description:
          "Sila muat naik bahan pemeriksaan anda, saya akan memberikan rujukan tafsiran terperinci! Adakah anda memerlukan bantuan menganalisis laporan imej perubatan hari ini?",
      },
    },
    // 侧边栏UI
    newChat: "Sembang Baru",
    functionModules: "Modul Fungsi",
    historyChats: "Sejarah Sembang",
    expandSidebar: "Kembangkan Bar Sisi",
    collapseSidebar: "Lipat Bar Sisi",
    homeTooltip: "Laman Utama Doktor Veterinar Baik",
    confirmDelete:
      'Adakah anda pasti mahu memadamkan perbualan "{title}"? Tindakan ini tidak dapat dibuat asal.',
    deleteTitle: "Sahkan Pemadaman",
    confirmDeleteButton: "Padam",
    cancelButton: "Batal",
    logoutConfirm: "Adakah anda pasti mahu log keluar?",
    logoutTitle: "Pengesahan Log Keluar",
    confirmButton: "Sahkan",
    logoutSuccess: "Log keluar berjaya",
    loadMore: "Tarik ke bawah untuk muatkan lebih",
    noMoreData: "Tiada data lagi",
    feedback: "Maklum Balas",
    logout: "Log Keluar",
    login: "Log Masuk",
    rename: "Namakan Semula",
    delete: "Padam",
  },
  // 印尼语
  id: {
    // 功能模块
    functions: {
      chat: {
        title: "Asisten Diagnosis Diferensial",
        subTitle:
          "Saya Asisten AI Vet Mind. Ada yang bisa saya bantu hari ini?",
        description:
          "Memberikan diagnosis cerdas dan saran profesional melalui deskripsi gejala dan hasil pemeriksaan",
      },
      knowledge: {
        title: "Asisten Pengetahuan AI",
        subTitle:
          "Saya adalah asisten pencarian pengetahuan, apa yang perlu saya bantu cari?",
        description:
          "Silakan tanya saya tentang penyakit, obat-obatan dan pengetahuan terkait~",
      },
      voice: {
        title: "Input Rekam Medis Suara",
        subTitle: "Mulai Konsultasi",
        description:
          "Klik tombol di bawah untuk mulai merekam proses konsultasi Anda, sistem akan secara otomatis menyalin ke teks dan membuat rekam medis",
      },
      image: {
        title: "Asisten Diagnosis Pencitraan",
        subTitle: "Asisten Diagnosis Pencitraan",
        description:
          "Silakan unggah materi pemeriksaan Anda, saya akan memberikan referensi interpretasi terperinci! Apakah Anda memerlukan bantuan menganalisis laporan pencitraan medis hari ini?",
      },
    },
    // 侧边栏UI
    newChat: "Obrolan Baru",
    functionModules: "Modul Fungsi",
    historyChats: "Riwayat Obrolan",
    expandSidebar: "Perluas Sidebar",
    collapseSidebar: "Lipat Sidebar",
    homeTooltip: "Beranda Dokter Hewan Baik",
    confirmDelete:
      'Apakah Anda yakin ingin menghapus percakapan "{title}"? Tindakan ini tidak dapat dibatalkan.',
    deleteTitle: "Konfirmasi Penghapusan",
    confirmDeleteButton: "Hapus",
    cancelButton: "Batal",
    logoutConfirm: "Apakah Anda yakin ingin keluar?",
    logoutTitle: "Konfirmasi Keluar",
    confirmButton: "Konfirmasi",
    logoutSuccess: "Keluar berhasil",
    loadMore: "Tarik ke bawah untuk memuat lebih",
    noMoreData: "Tidak ada data lagi",
    feedback: "Umpan Balik",
    logout: "Keluar",
    login: "Masuk",
    rename: "Ubah Nama",
    delete: "Hapus",
  },
  // 越南语
  vi: {
    // 功能模块
    functions: {
      chat: {
        title: "Trợ lý Chẩn đoán Phân biệt",
        subTitle:
          "Tôi là Trợ lý AI của Vet Mind. Tôi có thể giúp gì cho bạn hôm nay?",
        description:
          "Cung cấp chẩn đoán thông minh và lời khuyên chuyên nghiệp thông qua mô tả triệu chứng và kết quả khám",
      },
      knowledge: {
        title: "Trợ lý Kiến thức AI",
        subTitle: "Tôi là trợ lý tìm kiếm kiến thức, bạn cần tôi giúp tìm gì?",
        description:
          "Cứ hỏi tôi về bệnh tật, thuốc và kiến thức liên quan nhé~",
      },
      voice: {
        title: "Nhập Hồ sơ Y tế Bằng Giọng nói",
        subTitle: "Bắt đầu Khám",
        description:
          "Nhấp nút bên dưới để bắt đầu ghi lại quá trình khám của bạn, hệ thống sẽ tự động chuyển thành văn bản và tạo hồ sơ y tế",
      },
      image: {
        title: "Trợ lý Chẩn đoán Hình ảnh",
        subTitle: "Trợ lý Chẩn đoán Hình ảnh",
        description:
          "Vui lòng tải lên tài liệu khám của bạn, tôi sẽ cung cấp thông tin giải thích chi tiết! Hôm nay có cần giúp phân tích báo cáo hình ảnh y tế không?",
      },
    },
    // 侧边栏UI
    newChat: "Trò chuyện Mới",
    functionModules: "Mô-đun Chức năng",
    historyChats: "Lịch sử Trò chuyện",
    expandSidebar: "Mở rộng Thanh bên",
    collapseSidebar: "Thu gọn Thanh bên",
    homeTooltip: "Trang chủ Bác sĩ Thú y Giỏi",
    confirmDelete:
      'Bạn có chắc chắn muốn xóa cuộc trò chuyện "{title}"? Hành động này không thể hoàn tác.',
    deleteTitle: "Xác nhận Xóa",
    confirmDeleteButton: "Xóa",
    cancelButton: "Hủy",
    logoutConfirm: "Bạn có chắc chắn muốn đăng xuất?",
    logoutTitle: "Xác nhận Đăng xuất",
    confirmButton: "Xác nhận",
    logoutSuccess: "Đăng xuất thành công",
    loadMore: "Kéo xuống để tải thêm",
    noMoreData: "Không có dữ liệu thêm",
    feedback: "Phản hồi",
    logout: "Đăng xuất",
    login: "Đăng nhập",
    rename: "Đổi tên",
    delete: "Xóa",
  },
};
