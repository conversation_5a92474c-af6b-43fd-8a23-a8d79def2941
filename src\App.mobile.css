/* 移动端全局样式调整 */
#app {
  height: 100vh !important; /* 移动端使用 vh 而不是 dvh */
  height: 100dvh !important; /* 支持动态视口的浏览器 */
  overflow: hidden !important;
}

/* 移动端滚动条隐藏 */
*::-webkit-scrollbar {
  width: 0px !important;
  height: 0px !important;
  display: none !important;
}

/* 移动端布局调整 */
.page-layout {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  position: relative !important;
}

/* 移动端隐藏桌面端侧边栏 */
.page-slider {
  display: none !important;
}

/* 移动端内容区域 */
.page-content {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  background-color: #f8f9fa !important;
  overflow: hidden !important;
  position: relative !important;
}

/* 移动端菜单按钮 */
.mobile-menu-button {
  display: flex !important;
  position: fixed !important;
  top: 16px !important;
  left: 16px !important;
  z-index: 1001 !important;
  width: 44px !important;
  height: 44px !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  color: var(--el-text-color-primary) !important;
}

.mobile-menu-button:hover {
  background: rgba(255, 255, 255, 0.95) !important;
  transform: scale(1.05) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
}

.mobile-menu-button:active {
  transform: scale(0.95) !important;
}

.mobile-menu-button .el-icon {
  font-size: 20px !important;
}

/* 移动端抽屉遮罩 */
.mobile-drawer-overlay {
  display: block !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.5) !important;
  z-index: 1000 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(4px) !important;
}

.mobile-drawer-overlay.show {
  opacity: 1 !important;
  visibility: visible !important;
}

/* 移动端抽屉容器 */
.mobile-drawer {
  display: block !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
  width: 280px !important;
  max-width: 80vw !important;
  background: #ffffff !important;
  z-index: 1001 !important;
  transform: translateX(-100%) !important;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15) !important;
  overflow: hidden !important;
}

.mobile-drawer.show {
  transform: translateX(0) !important;
}

/* 移动端抽屉内容 */
.mobile-drawer .app-slider {
  height: 100% !important;
  width: 100% !important;
  border-right: none !important;
  box-shadow: none !important;
}

/* 移动端安全区域适配 */
@supports (padding-top: env(safe-area-inset-top)) {
  .mobile-menu-button {
    top: calc(16px + env(safe-area-inset-top)) !important;
  }
  
  .mobile-drawer {
    padding-top: env(safe-area-inset-top) !important;
  }
}

@supports (padding-left: env(safe-area-inset-left)) {
  .mobile-menu-button {
    left: calc(16px + env(safe-area-inset-left)) !important;
  }
  
  .mobile-drawer {
    padding-left: env(safe-area-inset-left) !important;
    width: calc(280px + env(safe-area-inset-left)) !important;
  }
}

/* 横屏模式适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .mobile-menu-button {
    top: 12px !important;
    left: 12px !important;
    width: 40px !important;
    height: 40px !important;
  }
  
  .mobile-menu-button .el-icon {
    font-size: 18px !important;
  }
  
  .mobile-drawer {
    width: 260px !important;
    max-width: 75vw !important;
  }
}

/* 小屏幕设备适配 */
@media (max-width: 480px) {
  .mobile-drawer {
    width: 260px !important;
    max-width: 85vw !important;
  }
  
  .mobile-menu-button {
    width: 40px !important;
    height: 40px !important;
  }
  
  .mobile-menu-button .el-icon {
    font-size: 18px !important;
  }
}

/* 超小屏幕设备适配 */
@media (max-width: 360px) {
  .mobile-drawer {
    width: 240px !important;
    max-width: 90vw !important;
  }
  
  .mobile-menu-button {
    top: 12px !important;
    left: 12px !important;
    width: 36px !important;
    height: 36px !important;
  }
  
  .mobile-menu-button .el-icon {
    font-size: 16px !important;
  }
}

/* 防止页面滚动穿透 */
body.drawer-open {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}
