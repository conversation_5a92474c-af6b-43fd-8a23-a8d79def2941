import { LANGUAGE_OPTIONS } from "@/constants/constant";
import LoginService from "@/services/loginService";
import { TokenCookieManager } from "@/utils/cookieUtils";
import { getCurLanguage, setCurLanguage } from "@/utils/storageUtils";
import { defineStore } from "pinia";
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";

export const useCommonStore = defineStore("common", () => {
  // 控制登录弹框的显示/隐藏状态
  const showLogin = ref(false);
  const loginLoading = ref(false);
  const userInfo = ref();

  // 响应式的登录状态
  const isLogin = ref(TokenCookieManager.isLoggedIn());

    // 是否是移动端判断
  const isMobile = computed(() => {
    return window.innerWidth <= 768;
  });

  // 语言相关
  const { locale } = useI18n();

  // 登录方法
  const login = async (
    phone: string,
    captcha: string,
    remember: boolean = false
  ) => {
    try {
      loginLoading.value = true;
      const result = await LoginService.loginFromApi(phone, captcha);
      if (result.access_token) {
        // 使用新的 Cookie 管理器存储 token
        TokenCookieManager.setToken(
          result.access_token,
          remember,
          result.refresh_token
        );
        // 更新响应式登录状态
        isLogin.value = true;
        // 登录成功后关闭登录弹框
        showLogin.value = false;
        window.location.reload();
      }
    } finally {
      loginLoading.value = false;
    }
  };

  // 退出登录方法
  const logout = () => {
    TokenCookieManager.removeToken();
    isLogin.value = false;
    window.location.reload();
  };

  // 检查用户是否已登录（基于 token 存在性）
  const isLoggedIn = (): boolean => {
    return TokenCookieManager.isLoggedIn();
  };

  const getLoginUserInfo = async () => {
    const result = await LoginService.getLoginUserInfoFromApi();
    userInfo.value = result
  };

  // 语言切换相关方法
  const handleLanguageChange = (value: string) => {
    locale.value = value;
    setCurLanguage(value);
  };

  // 获取当前语言的显示信息
  const currentLanguageInfo = computed(() => {
    return LANGUAGE_OPTIONS.find(option => option.value === locale.value) || LANGUAGE_OPTIONS[0];
  });

  // 获取浏览器默认语言并设置初始语言
  const getBrowserLanguage = (): string => {
    // 获取浏览器语言
    const browserLang = navigator.language || navigator.languages?.[0] || 'zh';
    // 提取语言前缀（例如：zh-CN -> zh）
    const langPrefix = browserLang.split('-')[0].toLowerCase();
    // 在 LANGUAGE_OPTIONS 中查找匹配的语言
    const matchedLanguage = LANGUAGE_OPTIONS.find(option => option.value === langPrefix);
    // 如果匹配不上，返回默认语言
    return matchedLanguage?.value || LANGUAGE_OPTIONS[0].value;
  };

  // 初始化语言设置
  const initializeLanguage = () => {
    const language = getCurLanguage();
    if (!language) {
      const browserLang = getBrowserLanguage();
      console.log('检测到浏览器语言:', navigator.language, '-> 设置为:', browserLang);
      locale.value = browserLang;
      setCurLanguage(browserLang);
    } else {
      locale.value = language;
    }
  };

  return {
    showLogin,
    loginLoading,
    isLogin,
    userInfo,
    currentLanguageInfo,
    isMobile,
    login,
    logout,
    isLoggedIn,
    getLoginUserInfo,
    // 语言相关
    handleLanguageChange,
    getBrowserLanguage,
    initializeLanguage,
  };
});
