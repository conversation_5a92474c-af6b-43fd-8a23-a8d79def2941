<script lang="ts" setup>
import AppSlider from "@/components/app/AppSlider.vue";
import AppSliderMobile from "@/components/app/AppSliderMobile.vue";
import LoginDialog from "@/components/app/LoginDialog.vue";
import { useCommonStore } from "@/stores/common";
import { TokenCookieManager } from "@/utils/cookieUtils";
import { storeToRefs } from "pinia";
import { computed, onMounted } from "vue";
import { useRoute } from "vue-router";
import { HIDDEN_LAYOUT_PATH } from "./constants/constant";
import { useSliderStore } from "./stores/slider";

// 获取当前路由对象
const route = useRoute();

// 全局的Pina库
const commonStore = useCommonStore();
const sliderStore = useSliderStore();

// 全局响应数据
const { loading } = storeToRefs(sliderStore);
const { isMobile, isLogin, showLogin } = storeToRefs(commonStore);

// 本组件的响应式数据
const showLayout = computed(() => {
  return HIDDEN_LAYOUT_PATH.includes(route.path) ? false : true;
});

// 初始化应用状态
const initializeApp = () => {
  const token = TokenCookieManager.getToken();
  if (token) {
    // 如果存在 token，设置到 API 请求头中
    import("@/utils/api").then(({ default: api }) => {
      api.setHeader("Authorization", `Bearer ${token}`);
    });
  }

  // 如果没有 token，显示登录弹框
  if (!isLogin.value) {
    showLogin.value = true;
  }
};

onMounted(() => {
  initializeApp();
});
</script>

<template>
  <div v-if="showLayout" class="page-layout">
    <div class="page-slider">
      <AppSliderMobile v-if="isMobile" />
      <AppSlider v-else />
    </div>
    <div class="page-content" v-loading="loading">
      <router-view />
    </div>
    <LoginDialog />
  </div>
  <div v-else class="no-page-layout">
    <router-view />
  </div>
</template>

<style src="./App.css"></style>
