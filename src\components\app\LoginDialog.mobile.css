:deep(.el-dialog > header) {
  display: none;
}

.login-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60dvh;
  position: relative;
  overflow: hidden;
  border-radius: 4dvw;
}

/* 语言切换器样式 */
.language-switcher {
  position: absolute;
  top: 2dvh;
  right: 3dvw;
  z-index: 10;
}

.globe-icon {
  --icon-size: 8dvw;
  color: rgba(67, 170, 255);
  animation: rotateGlobe 8s linear infinite;
}

@keyframes rotateGlobe {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Popover 内容样式 */
.language-option {
  display: flex;
  align-items: center;
  padding: 2dvw 4dvw;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
  color: #333333;
}

.language-option:hover {
  background-color: rgba(135, 206, 235, 0.1);
}

.language-option.active {
  background-color: rgba(135, 206, 235, 0.2);
  color: #333333;
}

.language-option .flag {
  margin-right: 3dvw;
  font-size: 5dvw;
}

.language-option .label {
  font-size:  3.5dvw;
  font-weight: 500;
  color: inherit;
}

/* Add futuristic animated background overlay */
.login-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%,
      rgba(0, 255, 255, 0.1) 0%,
      transparent 50%),
    radial-gradient(circle at 80% 20%,
      rgba(0, 87, 255, 0.1) 0%,
      transparent 50%),
    radial-gradient(circle at 40% 40%,
      rgba(255, 255, 255, 0.05) 0%,
      transparent 50%);
  animation: backgroundPulse 4s ease-in-out infinite alternate;
  pointer-events: none;
}

@keyframes backgroundPulse {
  0% {
    opacity: 0.3;
  }

  100% {
    opacity: 0.7;
  }
}

.login-content-right {
  display: flex;
  flex-direction: column;
  flex: 1;
  background: #ffffff;
  height: 100%;
  align-items: center;
  justify-content: center;
  gap: 5dvw;
  color: #333333;
  position: relative;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  padding-inline: 2dvw;
  box-shadow:
    inset 0 0 50px rgba(0, 0, 0, 0.05),
    0 0 50px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 右侧背景装饰元素 */
.login-content-right::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 80% 20%,
      rgba(135, 206, 235, 0.08) 0%,
      transparent 40%),
    radial-gradient(circle at 20% 80%,
      rgba(96, 151, 252, 0.06) 0%,
      transparent 40%),
    radial-gradient(circle at 60% 60%,
      rgba(0, 212, 255, 0.04) 0%,
      transparent 30%);
  animation: floatingBubbles 12s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.login-content-right::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(135deg,
      transparent 0%,
      rgba(135, 206, 235, 0.02) 25%,
      rgba(96, 151, 252, 0.03) 50%,
      rgba(0, 212, 255, 0.02) 75%,
      transparent 100%);
  animation: gradientShift 8s ease-in-out infinite alternate;
  pointer-events: none;
  z-index: 1;
}

@keyframes floatingBubbles {

  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
  }

  25% {
    transform: translate(-10px, -15px) rotate(90deg);
  }

  50% {
    transform: translate(15px, -10px) rotate(180deg);
  }

  75% {
    transform: translate(-5px, 10px) rotate(270deg);
  }
}

@keyframes gradientShift {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }

  100% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

.login-content-right-title {
  font-size: 6dvw;
  font-weight: bold;
  letter-spacing: 0.2dvw;
  color: #333333;
  position: relative;
  z-index: 3;
  margin-bottom: 2dvw;
  text-align: center;
  background: linear-gradient(135deg, #333333 0%, #666666 50%, #333333 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% {
    filter: drop-shadow(0 0 5px rgba(135, 206, 235, 0.3));
  }

  100% {
    filter: drop-shadow(0 0 15px rgba(96, 151, 252, 0.4));
  }
}

/* 右侧功能标签已移至左侧，此处样式已移除 */
.login-phone-group {
  width: 80%;
  display: flex;
  align-items: center;
  position: relative;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid rgba(135, 206, 235, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    0 0 0 0 rgba(135, 206, 235, 0);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  z-index: 2;
}

.login-phone-group::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(135, 206, 235, 0.05) 0%, rgba(96, 151, 252, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.login-phone-group:focus-within {
  border-color: #87ceeb;
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 0 30px rgba(135, 206, 235, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.9),
    0 0 0 3px rgba(135, 206, 235, 0.1);
  transform: translateY(-3px) scale(1.02);
}

.login-phone-group:focus-within::before {
  opacity: 1;
}

.login-phone-select {
  width: 35%;
  height: 100%;
}

:deep(.login-phone-select > div:first-child) {
  height: 100%;
  border-radius: 12px 0 0 12px;
}

.login-phone-input {
  width: 75%;
  padding-block: 1.2dvw;
}

/* Enhanced Element Plus input styling */
.login-phone-group :deep(.el-input__wrapper) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.login-phone-group :deep(.el-input__inner) {
  background: transparent !important;
  border: none !important;
  color: #333333 !important;
  letter-spacing: 0.05dvw;
  font-size: 3.4dvw;
}

.login-phone-group :deep(.el-input__inner::placeholder) {
  color: rgba(102, 102, 102, 0.8) !important;
  font-weight: 400;
}

.login-phone-group :deep(.el-select .el-input__wrapper) {
  background: transparent !important;
  border-right: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.login-phone-area-code {
  float: left;
  margin-right: 1.5dvw;
}

.login-phone-area-place {
  float: right;
  color: rgba(102, 102, 102, 0.7);
  font-size: 3dvw;
}

.login-captcha-group {
  display: flex;
  width: 80%;
  position: relative;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid rgba(135, 206, 235, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    0 0 0 0 rgba(135, 206, 235, 0);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  z-index: 2;
}

.login-captcha-group::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(135, 206, 235, 0.05) 0%, rgba(96, 151, 252, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.login-captcha-group:focus-within {
  border-color: #87ceeb;
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 0 30px rgba(135, 206, 235, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.9),
    0 0 0 3px rgba(135, 206, 235, 0.1);
  transform: translateY(-3px) scale(1.02);
}

.login-captcha-group:focus-within::before {
  opacity: 1;
}

.login-captcha-input {
  padding-block: 1.2dvw;
}

.login-captcha-group :deep(.el-input__wrapper) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.login-captcha-group :deep(.el-input__inner) {
  background: transparent !important;
  border: none !important;
  color: #333333 !important;
  letter-spacing: 0.05dvw;
  font-size: 3.4dvw;
}

.login-captcha-group :deep(.el-input__inner::placeholder) {
  color: rgba(102, 102, 102, 0.8) !important;
  font-weight: 400;
}

.login-captcha-button {
  width: 45%;
  background: linear-gradient(135deg, #87ceeb 0%, #6097fc 100%);
  color: white;
  border: none;
  border-radius: 0 12px 12px 0;
  font-size: 3.2dvw;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.05dvw;
  box-shadow:
    0 4px 15px rgba(135, 206, 235, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  z-index: 1;
}

.login-captcha-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent);
  transition: left 0.6s;
}

.login-captcha-button::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle,
      rgba(255, 255, 255, 0.3) 0%,
      transparent 70%);
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
  border-radius: 50%;
}

.login-captcha-button:hover:not(.disabled) {
  background: linear-gradient(135deg, #a0d8f0 0%, #80b3ff 100%);
  box-shadow:
    0 6px 20px rgba(135, 206, 235, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-1px) scale(1.02);
}

.login-captcha-button:hover:not(.disabled)::before {
  left: 100%;
}

.login-captcha-button:hover:not(.disabled)::after {
  width: 100px;
  height: 100px;
}

.login-captcha-button.disabled {
  background: linear-gradient(135deg,
      rgba(135, 206, 235, 0.3) 0%,
      rgba(96, 151, 252, 0.3) 100%);
  cursor: not-allowed;
  opacity: 0.6;
  box-shadow: none;
}

.login-captcha-button:active:not(.disabled) {
  background: linear-gradient(135deg, #6bb6e8 0%, #4080ff 100%);
  color: white;
}

/* 登录按钮样式 - 渐变蓝色设计 */
.login-button {
  width: 65%;
  height: 6dvh;
  background: linear-gradient(135deg, #87ceeb 0%, #6097fc 100%);
  color: white;
  border: none;
  border-radius: 8dvw;
  font-size: 5.5dvw;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.2dvw;
  text-transform: uppercase;
  z-index: 2;
  box-shadow:
    0 8px 25px rgba(135, 206, 235, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.login-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent);
  transition: left 0.6s ease;
}

.login-button::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle,
      rgba(255, 255, 255, 0.4) 0%,
      transparent 70%);
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
  border-radius: 50%;
}

.login-button:hover:not(.disabled)::before {
  left: 100%;
}

.login-button:hover:not(.disabled)::after {
  width: 200px;
  height: 200px;
}

.login-button:hover:not(.disabled) {
  background: linear-gradient(135deg, #a0d8f0 0%, #80b3ff 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 35px rgba(135, 206, 235, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.login-button.disabled {
  background: linear-gradient(135deg,
      rgba(135, 206, 235, 0.3) 0%,
      rgba(96, 151, 252, 0.3) 100%);
  cursor: not-allowed;
  opacity: 0.6;
  box-shadow: none;
}

.login-button:active:not(.disabled) {
  background: linear-gradient(135deg, #6bb6e8 0%, #4080ff 100%);
  transform: translateY(0);
}

/* 复选框组样式 - Enhanced Design */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 1dvw;
  margin: 1dvw 0;
  width: 80%;
}

/* 用户协议复选框样式 */
.policy-checkbox {
  color: #333333;
  white-space: normal;
  display: flex;
  justify-content: center;
}

.policy-checkbox :deep(.el-checkbox__label) {
  color: #333333;
  font-size: 2.6dvw;
  letter-spacing: 0.1dvw;
}

.policy-checkbox :deep(.el-checkbox__input .el-checkbox__inner) {
  background: #ffffff;
  border: 1px solid #d0d7de;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.policy-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background: linear-gradient(135deg, #0057ff 0%, #00d4ff 100%);
  border-color: #00d4ff;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
}

/* 用户协议链接样式 - Enhanced Futuristic Design */
.policy-link {
  color: #399bfd;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  padding: 0 2px;
  border-radius: 3px;
}

.policy-link::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 1px;
  background: #399bfd;
  transition: width 0.3s ease;
}

.policy-link:hover::before {
  width: 90%;
}

/* Enhanced Element Plus Select Dropdown Styling */
:deep(.el-select-dropdown) {
  background: #ffffff !important;
  border: 1px solid #e9ecef !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
  color: #333333 !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background: rgba(135, 206, 235, 0.1) !important;
  color: #333333 !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item.selected) {
  background: rgba(135, 206, 235, 0.2) !important;
  color: #333333 !important;
}

/* Enhanced Element Plus Select Input Styling */
.login-phone-group :deep(.el-select .el-input .el-input__wrapper) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.login-phone-group :deep(.el-select .el-input .el-input__inner) {
  color: #333333 !important;
  background: transparent !important;
  letter-spacing: 0.05dvw;
}

.login-phone-group :deep(.el-select .el-input .el-input__suffix) {
  color: rgba(102, 102, 102, 0.7) !important;
}

/* 语言切换 Popover 全局样式 */
:deep(.el-popover.language-popover) {
  background: #ffffff !important;
  border: 1px solid #e9ecef !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
  padding: 0 !important;
}

:deep(.el-popover.language-popover .el-popover__arrow::before) {
  background: #ffffff !important;
  border: 1px solid #e9ecef !important;
}