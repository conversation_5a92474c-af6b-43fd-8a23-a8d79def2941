#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100dvh;
  overflow: hidden;
}

/* 全局滚动条样式 - 悬停显示 */
*::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

*::-webkit-scrollbar-track {
  border-radius: 8px;
  transition: all 0.3s ease;
}

*::-webkit-scrollbar-thumb {
  border-radius: 0.8dvw;
  transition: all 0.3s ease;
}

*:hover::-webkit-scrollbar-thumb {
  background: var(--el-color-info-light-3);
}

body {
  margin: 0;
}

.page-layout {
  display: flex;
  height: 100%;
}

.no-page-layout {
  overflow: auto;
  height: 100%;
}

.page-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}