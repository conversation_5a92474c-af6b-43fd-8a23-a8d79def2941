.header-container {
  height: 6dvh;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  align-items: center;
  /* background-color: white; */
  /* box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08); */
}

.header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  margin: auto;
  height: 100%;
  height: 6dvh;
}

.header-right {
  display: flex;
  justify-content: right;
  padding-right: 24px;
  align-items: center;
}

/* 语言切换器样式 */
.language-dropdown {
  cursor: pointer;
  position: relative;
}

.language-selector {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
  min-width: 120px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* .language-selector:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px 0 rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.language-dropdown:hover .dropdown-arrow {
  transform: rotate(180deg);
  color: #3b82f6;
} */

.globe-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
  margin-right: 8px;
  flex-shrink: 0;
}

.language-text {
  font-size: 14px;
  color: #374151;
  flex-grow: 1;
  font-weight: 500;
}

.dropdown-arrow {
  width: 16px;
  height: 16px;
  color: #9ca3af;
  margin-left: 4px;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.language-dropdown.is-active .dropdown-arrow {
  transform: rotate(180deg);
}

/* 下拉菜单样式 */
.language-dropdown-menu {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.12);
  padding: 4px 0;
  min-width: 140px;
  animation: dropdown-fade-in 0.2s ease-out;
  transform-origin: top center;
}

@keyframes dropdown-fade-in {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.language-option {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
}

.language-option:hover {
  background-color: #f3f4f6;
}

.language-option.active {
  background-color: #eff6ff;
  color: #2563eb;
}

.language-option .flag {
  margin-right: 12px;
  font-size: 16px;
}

.language-option .label {
  font-size: 14px;
  font-weight: 500;
  color: inherit;
  margin-left: 12px;
}
