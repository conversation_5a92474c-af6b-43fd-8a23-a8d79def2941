<script lang="ts" setup>
import Feedback from "@/components/common/Feedback.vue";
import LoginIcon from "@/components/icons/LoginIcon.vue";
import LogoutIcon from "@/components/icons/LogoutIcon.vue";
import NewChatIcon from "@/components/icons/NewChatIcon.vue";
import { ConversationType, FunctionType } from "@/constants/enums";
import { useCommonStore } from "@/stores/common";
import { ChatDotRound, Loading, Menu } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { storeToRefs } from "pinia";
import { computed, onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { FUNCTION_LIST } from "../../constants/constant";
import { type ConversationInfo } from "../../services/conversationService";
import { useConversationStore } from "../../stores/conversation";
import { useSliderStore } from "../../stores/slider";
import DeleteIcon from "../icons/DeleteIcon.vue";
import EditIcon from "../icons/EditIcon.vue";
import MoreDotsIcon from "../icons/MoreDotsIcon.vue";

// 全局的Pina库
const sliderStore = useSliderStore();
const conversationStore = useConversationStore();
const commonStore = useCommonStore();
const { t } = useI18n();

// 全局方法
const { clickFunction, clickHistory } = sliderStore;
const { getLoginUserInfo } = commonStore;
const {
  getConversationList,
  removeConversation,
  renameConversation,
  loadMoreConversationList,
} = conversationStore;

// 全局的响应数据
const { curFunction, curConversation } = storeToRefs(sliderStore);
const {
  pagination,
  conversationList,
  loading: conversationLoading,
} = storeToRefs(conversationStore);
const { userInfo } = storeToRefs(commonStore);

// 全局方法
const { clickNewChat } = sliderStore;

// 本组件的初始化
onMounted(async () => {
  if (curFunction.value.id === FunctionType.CHAT) {
    await getConversationList(1, ConversationType.DIAGNOSIS);
  } else if (curFunction.value.id === FunctionType.VOICE) {
    await getConversationList(1, ConversationType.VOICE);
  } else if (curFunction.value.id === FunctionType.KNOWLEDGE) {
    await getConversationList(1, ConversationType.KNOWLEDGE);
  }
  await getLoginUserInfo();
});

// 本组件的响应数据
const router = useRouter();
const renamingConversationId = ref<number | null>(null);
const renameInputValue = ref("");
const hasMore = computed(() => {
  const page = pagination.value.page;
  const pages = pagination.value.pages;
  return page && pages ? page < pages : true;
});
const isCollapsed = ref(true);
const showFeedbackDialog = ref(false);

// 本组件的方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};
const handleMenuCommand = async (command: string, item: ConversationInfo) => {
  if (command === "rename") {
    startRename(item);
  } else if (command === "delete") {
    await deleteConversation(item);
  }
};
const startRename = (item: ConversationInfo) => {
  renamingConversationId.value = item.id;
  renameInputValue.value = item.title;
};
const cancelRename = () => {
  renamingConversationId.value = null;
  renameInputValue.value = "";
};
const confirmRename = async (item: ConversationInfo) => {
  const newTitle = renameInputValue.value.trim();

  if (!newTitle || newTitle === item.title) {
    cancelRename();
    return;
  }

  try {
    await renameConversation(item.id, newTitle);
  } finally {
    cancelRename();
  }
};
const deleteConversation = async (item: ConversationInfo) => {
  await ElMessageBox.confirm(
    t("appSlider.confirmDelete", { title: item.title }),
    t("appSlider.deleteTitle"),
    {
      confirmButtonText: t("appSlider.confirmDeleteButton"),
      cancelButtonText: t("appSlider.cancelButton"),
      type: "warning",
      confirmButtonClass: "el-button--danger",
    }
  );

  await removeConversation(item.id);
  if (curFunction.value.id === FunctionType.CHAT) {
    await getConversationList(1, ConversationType.DIAGNOSIS);
  } else if (curFunction.value.id === FunctionType.KNOWLEDGE) {
    await getConversationList(1, ConversationType.KNOWLEDGE);
  } else if (curFunction.value.id === FunctionType.VOICE) {
    await getConversationList(1, ConversationType.VOICE);
  }
};
const loadMore = async () => {
  const curPage = pagination.value.page + 1;
  await loadMoreConversationList(curPage);
  const historyList = document.querySelector(".history-list");
  if (historyList) {
    historyList.scrollTop = historyList.scrollTop - 50;
  }
};
const handleFeedback = () => {
  showFeedbackDialog.value = true;
};
const goToHome = () => {
  router.push("/");
};
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      t("appSlider.logoutConfirm"),
      t("appSlider.logoutTitle"),
      {
        confirmButtonText: t("appSlider.confirmButton"),
        cancelButtonText: t("appSlider.cancelButton"),
        type: "warning",
      }
    );
    commonStore.logout();
    ElMessage.success(t("appSlider.logoutSuccess"));
  } catch (error) {
    // 用户取消退出，不做任何操作
  }
};
const showLoginDialog = () => {
  commonStore.showLogin = true;
};
const handleCommand = (command: string) => {
  switch (command) {
    case "logout":
      handleLogout();
      break;
    case "login":
      showLoginDialog();
      break;
    case "feedback":
      handleFeedback();
      break;
    default:
      break;
  }
};

const beforeClose = () => {
  isCollapsed.value = true;
};
</script>

<template>
  <el-button
    class="mobile-slider-button"
    @click="isCollapsed = !isCollapsed"
    type="primary"
    :icon="Menu"
    circle
  >
  </el-button>
  <div class="mobile-slider-container">
    <el-drawer
      direction="ltr"
      :model-value="!isCollapsed"
      :with-header="false"
      :before-close="beforeClose"
      size="70%"
      body-class="drawer-body"
    >
      <div class="slider">
        <div class="slider-logo">
          <el-tooltip :content="t('appSlider.homeTooltip')" placement="right">
            <img
              class="slider-logo-img"
              src="/text_logo_cn.png"
              alt="logo"
              @click="goToHome"
            />
          </el-tooltip>

          <!-- 展开状态下的折叠按钮 -->
          <el-tooltip
            :content="t('appSlider.collapseSidebar')"
            placement="right"
          >
            <el-button
              type="text"
              @click="toggleCollapse"
              class="collapse-button-header"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1em"
                height="1em"
                fill="none"
                viewBox="0 0 20 20"
                class="btn-icon expand-icon"
              >
                <rect
                  width="16.799"
                  height="14.822"
                  x="1.601"
                  y="2.589"
                  stroke="currentColor"
                  stroke-width="1.875"
                  rx="2.75"
                ></rect>
                <path
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-width="1.875"
                  d="M9.069 3.412v13.175M4.402 7.177h1.866M4.402 10h1.866M4.402 12.823h1.866"
                ></path>
              </svg>
            </el-button>
          </el-tooltip>
        </div>
        <div class="new-chat">
          <el-tooltip
            :content="t('appSlider.newChat')"
            placement="right"
            :disabled="!isCollapsed"
          >
            <button class="new-chat-button" @click="clickNewChat">
              <NewChatIcon style="font-size: 4dvw" />
              <span>{{ t("appSlider.newChat") }}</span>
            </button>
          </el-tooltip>
        </div>
        <!-- 功能模块区域 -->
        <div class="slider-function">
          <div class="section-header">
            <h3>{{ t("appSlider.functionModules") }}</h3>
          </div>
          <ul class="function-list">
            <li
              v-for="item in FUNCTION_LIST"
              :key="item.id"
              @click="clickFunction(item)"
              :class="{ active: curFunction.id === item.id }"
            >
              <el-tooltip
                :content="t(item.title)"
                placement="right"
                :disabled="!isCollapsed"
              >
                <div class="function-item-content">
                  <div class="function-icon">
                    <component :is="item.icon" />
                  </div>
                  <span class="function-text">{{
                    t(item.title)
                  }}</span>
                </div>
              </el-tooltip>
            </li>
          </ul>
        </div>

        <!-- 历史对话区域 -->
        <div
          v-loading="conversationLoading"
          element-loading-background="rgba(255, 255, 255, 0.3)"
          class="slider-history"
        >
          <div class="section-header">
            <h3>{{ t("appSlider.historyChats") }}</h3>
          </div>
          <ul
            class="history-list"
            v-infinite-scroll="loadMore"
            :infinite-scroll-immediate="false"
            :infinite-scroll-disabled="!hasMore"
            :infinite-scroll-delay="2000"
          >
            <li
              v-for="item in conversationList"
              :key="item.id"
              :class="{ active: curConversation?.id === item.id }"
              class="conversation-item"
              @click="clickHistory(item)"
            >
              <el-tooltip
                :content="item.title"
                placement="right"
                :show-after="100"
              >
                <div class="conversation-content">
                  <!-- 重命名输入框 -->
                  <el-input
                    v-if="renamingConversationId === item.id"
                    v-model="renameInputValue"
                    size="small"
                    @blur="cancelRename"
                    @keyup.enter="confirmRename(item)"
                    @keyup.esc="cancelRename"
                    @click.stop
                    class="rename-input"
                    autofocus
                  />
                  <!-- 正常显示标题 -->
                  <span v-else class="conversation-title">{{
                    item.title
                  }}</span>
                </div>
              </el-tooltip>

              <!-- 三点菜单 -->
              <el-dropdown
                @command="(command: string) => handleMenuCommand(command, item)"
                trigger="click"
                placement="bottom-end"
                class="conversation-menu"
                @click.stop
              >
                <el-button
                  type="text"
                  size="small"
                  class="menu-button"
                  @click.stop
                >
                  <MoreDotsIcon />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu class="conversation-dropdown-menu">
                    <el-dropdown-item command="rename" class="rename-item">
                      <EditIcon />
                      <span style="margin-left: 0.3dvw; color: #f56c6c">{{
                        t("appSlider.rename")
                      }}</span>
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" class="delete-item">
                      <DeleteIcon />
                      <span style="margin-left: 0.3dvw">{{
                        t("appSlider.delete")
                      }}</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </li>
            <!-- 加载图标 -->
            <div v-if="hasMore" class="loading-container">
              {{ t("appSlider.loadMore") }}
              <el-icon class="loading-icon">
                <Loading />
              </el-icon>
            </div>
            <div v-else class="loading-container">
              {{ t("appSlider.noMoreData") }}
            </div>
          </ul>
        </div>

        <!-- 底部区域 -->
        <div class="slider-footer">
          <div class="app-header-user">
            <!-- 用户头像下拉菜单（登录和未登录状态都显示） -->
            <el-dropdown
              @command="handleCommand"
              trigger="click"
              placement="bottom-end"
              class="user-dropdown"
            >
              <div class="app-user-info">
                <el-avatar
                  src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
                  class="user-avatar"
                />
                <span >{{ userInfo?.username }}</span>
              </div>
              <template #dropdown>
                <el-dropdown-menu class="user-dropdown-menu">
                  <el-dropdown-item
                    v-if="commonStore.isLogin"
                    command="feedback"
                    class="menu-item"
                  >
                    <ChatDotRound style="width: 1rem" />
                    <span class="menu-text">{{ t("appSlider.feedback") }}</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="commonStore.isLogin"
                    command="logout"
                    class="menu-item"
                  >
                    <LogoutIcon />
                    <span class="menu-text">{{ t("appSlider.logout") }}</span>
                  </el-dropdown-item>
                  <el-dropdown-item v-else command="login" class="menu-item">
                    <LoginIcon />
                    <span class="menu-text">{{ t("appSlider.login") }}</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
        <Feedback v-model="showFeedbackDialog" />
      </div>
    </el-drawer>
  </div>
</template>

<style scoped src="./AppSliderMobile.css"></style>
